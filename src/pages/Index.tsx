import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Smartphone, TrendingUp, Shield, Zap, Users, CheckCircle, ArrowRight } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

const Index = () => {
  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email) {
      setIsSubmitted(true);
      toast({
        title: "You're on the waitlist! 🎉",
        description: "We'll notify you when we launch. Get ready to transform your finances!",
      });
      setEmail("");
    }
  };

  // Array of screenshot images for floating animation
  const screenshots = [
    "/lovable-uploads/7b99f60a-fc9c-420f-bbb3-7bbbc6df1525.png",
    "/lovable-uploads/a7727a15-cdc5-4868-86aa-afeda313738c.png",
    "/lovable-uploads/a3469f8f-c316-4610-b219-9b43c12a9856.png",
    "/lovable-uploads/3e61e230-74ce-48be-9433-03eafa07ceb2.png",
    "/lovable-uploads/64c3a956-1e8f-4822-b086-13b22f38d06a.png",
    "/lovable-uploads/97b5e4ff-4191-4b8a-8515-6e8ce4ece21a.png",
    "/lovable-uploads/5693a0c7-02a6-4267-b490-2c46f31415b0.png",
    "/lovable-uploads/02455264-b08c-4b8a-98c7-6065de024e51.png",
    "/lovable-uploads/1aa3795f-09ef-4fc0-9104-277861891039.png"
  ];

  return (
    <>
      <style>
        {`
          @keyframes float-diagonal {
            0% {
              transform: translate(-200px, 50px) rotate(12deg);
              opacity: 0;
            }
            5% {
              opacity: 0.3;
            }
            95% {
              opacity: 0.3;
            }
            100% {
              transform: translate(calc(100vw + 200px), -50px) rotate(12deg);
              opacity: 0;
            }
          }
          
          .animate-float-diagonal {
            animation: float-diagonal linear infinite;
          }
        `}
      </style>
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-100">
        {/* Hero Section */}
        <section className="relative overflow-hidden px-4 py-16 md:py-24">
          <div className="absolute inset-0 bg-gradient-to-r from-pink-600/10 via-purple-600/10 to-indigo-600/10" />
          <div className="relative mx-auto max-w-7xl">
            <div className="text-center">
              <Badge className="mb-6 bg-gradient-to-r from-pink-500 to-purple-500 text-white hover:from-pink-600 hover:to-purple-600">
                Coming Soon ✨
              </Badge>
              <h1 className="mb-6 text-4xl font-extrabold tracking-tight text-gray-900 md:text-6xl lg:text-7xl">
                Your Money,{" "}
                <span className="bg-gradient-to-r from-pink-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
                  Your Future
                </span>
              </h1>
              <p className="mx-auto mb-8 max-w-2xl text-lg text-gray-600 md:text-xl">
                The smartest way to save, invest, and grow your wealth. Built for the next generation of investors who want more from their money.
              </p>
              
              {/* Waitlist Form */}
              <div className="mx-auto max-w-md">
                {!isSubmitted ? (
                  <form onSubmit={handleSubmit} className="flex flex-col gap-4 sm:flex-row">
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="flex-1 h-12 text-base border-2 border-pink-200 focus:border-pink-500"
                      required
                    />
                    <Button 
                      type="submit" 
                      className="h-12 px-8 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white font-semibold"
                    >
                      Join Waitlist
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </form>
                ) : (
                  <div className="flex items-center justify-center gap-2 text-green-600 font-semibold">
                    <CheckCircle className="h-5 w-5" />
                    You're on the list! Check your email.
                  </div>
                )}
                <p className="mt-3 text-sm text-gray-500">
                  Join <span className="font-semibold text-pink-600">2,847</span> others waiting for early access
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Floating Screenshots Section */}
        <section className="relative px-4 py-32 bg-white/50 backdrop-blur-sm overflow-hidden">
          {/* Floating Screenshots Background */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {screenshots.map((screenshot, index) => (
              <div
                key={index}
                className="absolute animate-float-diagonal opacity-30 hover:opacity-50 transition-opacity duration-500"
                style={{
                  left: `${-15}%`,
                  top: `${5 + (index * 10)}%`,
                  animationDelay: `${index * 2.5}s`,
                  animationDuration: `${18 + (index * 1.5)}s`,
                  zIndex: 1
                }}
              >
                <img
                  src={screenshot}
                  alt={`App Screenshot ${index + 1}`}
                  className="w-40 h-auto rounded-2xl shadow-2xl transform rotate-12 hover:rotate-6 transition-transform duration-700"
                  loading="lazy"
                />
              </div>
            ))}
          </div>

          <div className="relative mx-auto max-w-7xl z-10" style={{ zIndex: 10 }}>
            <div className="text-center">
              <h2 className="text-4xl font-bold text-gray-900 mb-6">
                A Glimpse Into The Future
              </h2>
              <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
                See how our app will revolutionize the way you manage, save, and invest your money. 
                Experience the power of smart financial technology designed for your generation.
              </p>
              
              {/* Central Feature Highlights */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                <Card className="bg-gradient-to-br from-pink-500 to-rose-600 text-white border-0 shadow-xl">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <TrendingUp className="w-8 h-8" />
                    </div>
                    <h3 className="text-xl font-bold mb-2">Smart Investing</h3>
                    <p className="opacity-90">AI-powered portfolio management that grows with your goals</p>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-purple-500 to-indigo-600 text-white border-0 shadow-xl">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Zap className="w-8 h-8" />
                    </div>
                    <h3 className="text-xl font-bold mb-2">Round-Up Savings</h3>
                    <p className="opacity-90">Automatically save your spare change from every purchase</p>
                  </CardContent>
                </Card>

                <Card className="bg-gradient-to-br from-orange-500 to-pink-600 text-white border-0 shadow-xl">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Users className="w-8 h-8" />
                    </div>
                    <h3 className="text-xl font-bold mb-2">Social Features</h3>
                    <p className="opacity-90">Connect with friends and achieve financial goals together</p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="px-4 py-16">
          <div className="mx-auto max-w-7xl">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Why You'll Love Our App
              </h2>
              <p className="text-lg text-gray-600">
                Built by young people, for young people who want to take control of their financial future
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  icon: Smartphone,
                  title: "Mobile First",
                  description: "Designed for your lifestyle - manage money on the go"
                },
                {
                  icon: TrendingUp,
                  title: "Smart Investing",
                  description: "AI-powered recommendations tailored to your goals"
                },
                {
                  icon: Shield,
                  title: "Bank-Level Security",
                  description: "Your money and data are protected with military-grade encryption"
                },
                {
                  icon: Zap,
                  title: "Instant Everything",
                  description: "Real-time updates, instant transfers, immediate insights"
                }
              ].map((feature, index) => (
                <Card key={index} className="text-center hover:shadow-lg transition-all duration-300 border-2 border-transparent hover:border-pink-200">
                  <CardContent className="p-6">
                    <div className="w-12 h-12 bg-gradient-to-r from-pink-500 to-purple-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{feature.title}</h3>
                    <p className="text-gray-600">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Social Proof Section */}
        <section className="px-4 py-16 bg-gradient-to-r from-pink-600 to-purple-600">
          <div className="mx-auto max-w-4xl text-center">
            <div className="text-white">
              <h2 className="text-3xl font-bold mb-4">Join Thousands of Future Investors</h2>
              <p className="text-xl opacity-90 mb-8">
                Be part of the financial revolution that's changing how young people build wealth
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                {[
                  { number: "2,847", label: "People Waiting" },
                  { number: "95%", label: "Under 30" },
                  { number: "🔥", label: "Excitement Level" }
                ].map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-3xl font-bold mb-2">{stat.number}</div>
                    <div className="text-sm opacity-80">{stat.label}</div>
                  </div>
                ))}
              </div>
              
              {!isSubmitted && (
                <Button 
                  onClick={() => document.querySelector('input[type="email"]')?.scrollIntoView({ behavior: 'smooth' })}
                  size="lg" 
                  className="bg-white text-pink-600 hover:bg-gray-100 font-semibold px-8 py-3"
                >
                  <Users className="mr-2 h-5 w-5" />
                  Secure My Spot
                </Button>
              )}
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="px-4 py-8 bg-gray-900 text-white">
          <div className="mx-auto max-w-7xl text-center">
            <p className="text-sm opacity-60">
              © 2024 Your App Name. Built with ❤️ for the next generation of investors.
            </p>
          </div>
        </footer>
      </div>
    </>
  );
};

export default Index;
