// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jnaysswpowcdojyhtawq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpuYXlzc3dwb3djZG9qeWh0YXdxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgyNzEzNTAsImV4cCI6MjA2Mzg0NzM1MH0.J_EQwAF29_uVXo61JlS891UmdJZRgbz5EsYDnWJIxlE";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);